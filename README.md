# NeoX 文档系统

本仓库是 NeoX 的官方文档系统，使用 [MkDocs](https://www.mkdocs.org/) 和 [Material 主题](https://squidfunk.github.io/mkdocs-material/) 构建，提供完整的开发指南和相关资源。

## 📖 仓库概述

### 主要内容

- **开发环境搭建指南**：详细的后端开发环境配置流程

- **自动化运维文档**：自动化发布平台配置和使用指南

- **流程拓扑图**：系统各模块的详细流程图和架构说明，采用标准化 Mermaid 语法

- **代码部署文档**：Git 仓库管理和代码获取说明

- **AWS 配置指南**：ECR 访问权限设置和配置

- **Docker 环境**：容器化开发环境部署指南

- **项目配置**：后端服务配置和验证步骤

- **版本管理**：系统版本更新历史和变更记录

### 技术特性

- 🌐 **完整中文支持**：所有文档使用中文编写

- 🎨 **现代化界面**：基于 Material Design 的美观主题

- 🔍 **全文搜索**：支持文档内容全文检索

- 📱 **响应式设计**：适配各种设备屏幕

- 🚀 **快速导航**：层级化章节组织，便于查阅

## 🚀 快速开始

### 前置要求

**对于使用者**：无需安装任何工具，直接使用浏览器即可查看文档。

**对于开发者**：需要安装 [uv](https://docs.astral.sh/uv/) - 一个现代化的 Python 包和项目管理工具。

#### 安装 uv

##### macOS 系统

**方法一：使用 Homebrew（推荐）**
```bash
brew install uv
```

**方法二：使用官方安装脚本**
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

##### Windows 系统

**方法一：使用 PowerShell**
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**方法二：使用 pip**
```bash
pip install uv
```

**方法三：下载可执行文件**

- 访问 [uv 官方发布页面](https://github.com/astral-sh/uv/releases)

- 下载适合您系统的可执行文件

- 将其添加到系统 PATH 中

#### 验证安装

安装完成后，验证 uv 是否正确安装：

```bash
uv --version
```

> 💡 **更多安装选项**：访问 [uv 官方文档](https://docs.astral.sh/uv/getting-started/installation/) 获取详细的安装指南。

### 使用文档

根据您的使用场景，选择相应的方式：

#### 👀 使用者（仅查看文档）

如果您只需要查看文档，**无需安装任何工具**，直接使用浏览器打开构建好的HTML文件：

1. **获取文档文件**
   ```bash
   <NAME_EMAIL>:neoxinc/neox-docs.git
   cd neox-docs/NeoX
   ```

2. **直接打开文档**
   ```bash
   # macOS
   open site/index.html
   
   # Windows
   start site/index.html
   
   # Linux
   xdg-open site/index.html
   ```

   或者在文件管理器中双击 `site/index.html` 文件即可在浏览器中查看完整文档。

#### 🔧 开发者（需要修改文档）

如果您需要修改或维护文档，可以选择以下任一方式：

**方式一：全局安装（推荐）**
```bash
uv tool install --with mkdocs-material --with mkdocs-mermaid2-plugin 'mkdocs[i18n]'
```

这会全局安装 MkDocs 及其依赖，您可以在任何地方使用 `mkdocs` 命令。

**方式二：临时运行**
```bash
# 启动文档服务器（临时安装并运行）
uvx --with mkdocs-material --with mkdocs-mermaid2-plugin 'mkdocs[i18n]' mkdocs serve
```

### 开发者：启动本地文档服务器

> 💡 **注意**：此部分仅适用于需要修改文档的开发者。普通使用者请直接查看上方"使用者"部分的说明。

1. **克隆仓库**
   ```bash
   <NAME_EMAIL>:neoxinc/neox-docs.git
   cd neox-docs
   ```

2. **进入文档目录**
   ```bash
   cd NeoX
   ```

3. **启动本地服务器**
   
   **方式一：使用全局安装的MkDocs**
   ```bash
   mkdocs serve
   ```
   
   **方式二：使用uvx临时运行**
   ```bash
   uvx --with mkdocs-material --with mkdocs-mermaid2-plugin 'mkdocs[i18n]' mkdocs serve
   ```

4. **访问文档**
   
   打开浏览器访问：http://127.0.0.1:8000

   您将看到完整的 NeoX 文档站点，包含所有章节和导航功能，并支持热重载等开发特性。

## 📁 项目结构

```
neox-docs/
├── README.md                   # 项目说明文件（本文件）
└── NeoX/                       # 文档项目根目录
    ├── mkdocs.yml              # MkDocs 配置文件
    └── docs/                   # 文档源文件目录
        ├── index.md            # 文档首页
        ├── 开发环境搭建/        # 开发环境配置
        │   └── 后端开发/       # 后端开发指南
        ├── 自动化运维/         # 自动化运维相关
        │   └── 自动化发布平台/ # 发布平台配置
        ├── 流程拓扑图/         # 系统流程图（标准化 Mermaid 语法）
        │   ├── 医疗后端/       # 医疗后端流程
        │   ├── 薬師丸賢太/     # 薬師丸賢太流程
        │   ├── スマート薬局/   # 智能药局相关流程
        │   │   ├── 通用模块/   # 通用模块流程
        │   │   └── 薬師丸撫子/ # 薬師丸撫子流程
        │   └── 自动化运维/     # 运维流程
        └── 关于/               # 系统信息
            └── 版本说明/       # 版本更新记录
```

## 🔧 常用命令

### 文档开发命令

```bash
# 启动开发服务器（支持热重载）
mkdocs serve

# 构建静态文档站点（生成site目录）
mkdocs build

# 查看帮助信息
mkdocs --help
```

> 💡 **提示**：`mkdocs build` 命令会在 `site/` 目录下生成静态HTML文件，这些文件可以直接在浏览器中打开，无需服务器。

### 高级配置

如果需要自定义端口或地址：

```bash
# 指定端口
mkdocs serve --dev-addr=127.0.0.1:8001

# 使用不同地址
mkdocs serve --dev-addr=0.0.0.0:8000
```

## 📝 文档编辑指南

### 添加新页面

1. 在 `docs/` 目录下创建新的 Markdown 文件

2. 在 `mkdocs.yml` 的 `nav` 部分添加页面引用

3. 重启开发服务器查看效果

### 文档格式规范

- 使用中文编写所有文档内容

- 代码块使用适当的语言标识符

- 图片放置在相应章节目录下

- 链接使用相对路径引用

## 🤝 贡献指南

### 提交更改

1. Fork 本仓库

2. 创建功能分支：`git checkout -b feature/your-feature`

3. 提交更改：`git commit -m 'feat: 添加新功能'`

4. 推送分支：`git push origin feature/your-feature`

5. 创建 Pull Request

### 代码规范

- 提交信息使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式

- 文档内容使用中文编写

- 代码示例保持简洁清晰

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. **查看文档**：首先查看相关章节的详细说明

2. **检查常见问题**：查看 [常见问题解答](NeoX/docs/开发环境搭建/后端开发/FAQ.md)

3. **检查版本**：确认使用的是最新版本的文档

4. **联系团队**：通过项目 Issues 或联系开发团队

## 📄 许可证

版权所有 © 2025 NeoX 开发团队。保留所有权利。

## 🔗 相关链接

- [MkDocs 官方文档](https://www.mkdocs.org/)

- [Material 主题文档](https://squidfunk.github.io/mkdocs-material/)

- [uv 官方文档](https://docs.astral.sh/uv/)

- [Markdown 语法指南](https://www.markdownguide.org/)

---

**当前版本**：v2025072801
**最后更新**：2025年7月28日
**维护团队**：NeoX 开发团队
